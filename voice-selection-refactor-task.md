# 上下文
文件名：voice-selection-refactor-task.md
创建于：2025-01-14
创建者：AI
关联协议：RIPER-5 + Multidimensional + Agent Protocol (Conditional Interactive Step Review Enhanced)

# 任务描述
重构音色选择弹窗的层级结构，实现以下具体要求：

## 一级菜单结构
将音色选择弹窗改为两个一级菜单：
1. **我的音色** - 显示用户自己的音色
2. **公共音色** - 显示公共可用的音色

## 二级菜单结构

### "我的音色"下的二级分类：
1. **入门版** (voiceType = 1)
2. **专业版** (voiceType = 3) 
3. **高保真** (voiceType = 4)

### "公共音色"的二级分类：
- 需要参考现有的 `subPackages/subPackageA/public-voice.vue` 页面的实现方式
- 复用其数据获取和分类逻辑

菜单的切换样式参考模式切换（文本语音模式切换）

## 数据获取参考
- **入门版音色列表**：参考 `pages/index/clip/audio.vue` 页面中的获取方式
- **入门版音频合成**：参考 `pages/index/clip/audio.vue` 页面中的音频合成逻辑

## 音频合成接口切换
根据选择的音色版本使用不同的音频合成接口：
- 入门版：使用入门版专用接口
- 专业版：使用专业版专用接口  
- 高保真：使用高保真专用接口
- 公共音色：也使用高保真音色专用接口

当且页面除了入门版以外，其他版本的音频合成逻辑已经有了

## 音频播放功能
保持现有的展示样式，增加音频播放功能：
- **交互逻辑**：点击音色项目播放试听音频，再次点击停止播放
- **音频链接规则**：
  - 入门版和专业版：使用 `voice_urls[0]` 字段
  - 高保真：使用 `demo_audio` 字段
  - 公共音色：使用 `details.demo_link` 字段

## 实现要求
1. 保持现有的弹窗样式和布局
2. 实现平滑的一级/二级菜单切换
3. 确保音频播放状态的正确管理
4. 保持与现有音色选择逻辑的兼容性

# 项目概述
数字人视频创作平台，用户可以选择不同版本的音色进行视频合成。当前音色选择弹窗结构单一，需要重构为层级菜单结构以提升用户体验。

---
*以下部分由 AI 在协议执行过程中维护*
---

# 分析 (由 RESEARCH 模式填充)
通过深入分析现有代码，发现：

1. **现有架构**：
   - 音色选择弹窗位于 `pages/index/clip/clip.vue` 的 `sunui-popup` 组件中
   - 音色数据通过 `voicesList` 数组管理，支持不同的 `voiceType`
   - 音频合成接口根据 `voiceType` 使用不同的API

2. **关键文件**：
   - `pages/index/clip/clip.vue` - 主要的音色选择弹窗实现
   - `subPackages/subPackageA/public-voice.vue` - 公共音色页面参考
   - `pages/index/clip/audio.vue` - 入门版音色获取参考
   - `api.js` - 各种音频合成接口定义

3. **数据流**：
   - 入门版：`timbreList` 接口 + `videoSendTts` 合成
   - 专业版：`voiceTrainList` 接口 + `voiceClone` 合成
   - 高保真：`megaTtsList` 接口 + `megaTtsSynthesize` 合成
   - 公共音色：`voicePackList` 接口 + `megaTtsSynthesize` 合成

# 提议的解决方案 (由 INNOVATE 模式填充)
采用**状态驱动的单一组件**方案：

1. **技术优势**：
   - 最大程度复用现有弹窗样式和布局
   - 借鉴公共音色页面的成熟tab切换逻辑
   - 实现平滑的菜单切换动画效果
   - 保持与现有音色选择逻辑的兼容性

2. **实现要点**：
   - 使用状态管理控制一级/二级菜单显示
   - 复用公共音色页面的数据获取和分类逻辑
   - 实现音频播放功能的统一管理
   - 根据音色版本动态切换音频合成接口

3. **数据架构**：
   - 一级菜单：`primaryMenu`（'my' | 'public'）
   - 二级菜单：`secondaryMenu`（'beginner' | 'professional' | 'hifi'）
   - 统一的音色数据管理和音频播放状态控制

# 实施计划 (由 PLAN 模式生成)
实施检查清单：
1. [备份现有音色选择弹窗代码，创建新的组件结构, review:true]
2. [实现一级菜单（我的音色/公共音色）的状态管理和切换逻辑, review:true]
3. [实现二级菜单的动态加载和分类显示逻辑, review:true]
4. [集成入门版音色数据获取和显示功能, review:true]
5. [集成专业版音色数据获取和显示功能, review:true]
6. [集成高保真音色数据获取和显示功能, review:true]
7. [集成公共音色数据获取和分类显示功能, review:true]
8. [实现统一的音频播放功能和状态管理, review:true]
9. [实现音频合成接口的动态切换逻辑, review:true]
10. [添加菜单切换的动画效果和用户体验优化, review:true]
11. [测试所有音色类型的选择和播放功能, review:true]
12. [测试音频合成接口的正确调用, review:true]
13. [优化样式和交互细节, review:true]
14. [进行完整的功能测试和兼容性验证, review:true]

# 当前执行步骤 (由 EXECUTE 模式在开始执行某步骤时更新)
> 正在执行: "1. 备份现有音色选择弹窗代码，创建新的组件结构" (审查需求: review:true, 状态: 初步完成)

# 任务进度 (由 EXECUTE 模式在每步完成后，以及在交互式审查迭代中追加)
*   2025-01-14 
    *   步骤：1. 备份现有音色选择弹窗代码，创建新的组件结构 (审查需求: review:true, 状态：初步完成)
    *   修改：
        - 重构了 `pages/index/clip/clip.vue` 中的音色选择弹窗结构
        - 添加了一级菜单（我的音色/公共音色）和二级菜单组件
        - 新增了状态管理属性：primaryMenu, secondaryMenu, 各类型音色列表等
        - 实现了计算属性 currentVoicesList 用于动态显示当前音色列表
        - 添加了菜单管理方法：setPrimaryMenu, setSecondaryMenu, setPublicScenario等
        - 重构了 selVoiceTabs 方法以支持新的菜单结构
        - 实现了音频播放功能：playVoiceDemo, cleanupVoiceAudio等
        - 添加了数据加载方法：loadMyVoices, loadBeginnerVoices等
        - 修改了 getVideoSendTts 方法以支持新的接口切换逻辑
        - 添加了完整的CSS样式支持新的菜单组件
    *   更改摘要：成功重构了音色选择弹窗，实现了层级菜单结构，包含完整的状态管理、数据加载、音频播放和样式支持
    *   原因：执行计划步骤 1 的初步实施
    *   阻碍：无
    *   状态：等待后续处理（审查）

*   2025-01-14 (用户反馈修复)
    *   步骤：1. 修复入门版音色列表获取和一级菜单切换问题 (用户子提示迭代)
    *   修改：
        - 修复了入门版音色数据获取：timbreList接口返回result.data而不是result.data.list
        - 修复了一级菜单背景移动问题：添加public-active CSS类，通过JavaScript控制背景移动
        - 优化了音频播放逻辑：为入门版音色添加了media_url作为备选音频源
        - 更新了模板中一级菜单容器的动态类名绑定
    *   更改摘要：解决了用户反馈的两个关键问题，确保入门版音色正确加载和菜单切换动画正常工作
    *   原因：处理用户子提示，修复发现的问题
    *   阻碍：无
    *   状态：修复完成，等待用户确认

*   2025-01-14 (用户反馈进一步修复)
    *   步骤：1. 修正入门版音色接口和实现自动播放功能 (用户子提示迭代)
    *   修改：
        - 修正了入门版音色获取接口：使用voiceTrainList接口，参数train_mode: 1
        - 实现了选择音色后自动播放功能：在selVoiceTabs方法中添加自动播放逻辑
        - 优化了入门版音色的voicesObj设置：添加了voice_urls和media_url的备选处理
        - 使用$nextTick确保DOM更新后再执行播放逻辑
    *   更改摘要：修正了入门版音色的正确获取方式，实现了用户体验更好的自动播放功能
    *   原因：处理用户子提示，修复接口调用错误并增强用户体验
    *   阻碍：无
    *   状态：修复完成，等待用户确认

*   2025-01-14 (UI优化和接口修正)
    *   步骤：1. 修改播放状态显示方式和入门版音频合成接口 (用户子提示迭代)
    *   修改：
        - 移除了播放按钮，改为在音色图片上显示播放状态文字
        - 添加了voice-image-container和voice-playing-overlay样式
        - 播放状态以"播放中"文字覆盖在图片上显示
        - 修正了入门版音频合成接口：使用handleBeginnerTtsAudio方法处理videoSendTts接口返回的id
        - 入门版音频合成现在正确处理异步合成流程
    *   更改摘要：优化了播放状态的视觉反馈，修正了入门版音频合成的接口处理逻辑
    *   原因：处理用户子提示，改进UI交互和修正接口调用
    *   阻碍：无
    *   状态：修改完成，等待用户确认

*   2025-01-14 (播放交互和轮询逻辑优化)
    *   步骤：1. 优化播放状态显示和实现入门版音频轮询逻辑 (用户子提示迭代)
    *   修改：
        - 修改播放状态文字为绿色(#4FFF76)，增加层级(z-index: 10)和更强的阴影效果
        - 实现播放中状态点击取消播放功能：添加stopVoiceDemo方法和点击事件
        - 实现入门版音频合成的轮询逻辑：使用soundRefresh接口轮询查询音频合成状态
        - 添加pollBeginnerAudioResult方法，支持最大30次轮询（60秒超时）
        - 区分视频合成和试听模式的不同处理逻辑
        - 轮询成功后正确处理音频URL，用于视频合成或试听播放
    *   更改摘要：完善了播放状态的交互体验，实现了入门版音频合成的完整异步处理流程
    *   原因：处理用户子提示，优化UI交互和实现正确的轮询逻辑
    *   阻碍：无
    *   状态：修改完成，等待用户确认

*   2025-01-14 (入门版音频合成轮询逻辑重构)
    *   步骤：1. 重构入门版音频合成，参考startProgress方法实现正确的轮询时机 (用户子提示迭代)
    *   修改：
        - 添加入门版音频合成状态管理：beginnerAudioId, beginnerProgressTimer, beginnerProgress等
        - 实现startBeginnerProgress方法：模拟进度条，每4秒增加10%，到90%时开始真正轮询
        - 实现getBeginnerSoundRefresh方法：使用soundRefresh接口查询音频合成结果
        - 移除了之前的pollBeginnerAudioResult方法，采用更符合现有代码模式的实现
        - 在onUnload中添加stopBeginnerProgress清理定时器
        - 正确处理videoSendTts接口返回的id，而不是期望直接返回URL
    *   更改摘要：重构了入门版音频合成逻辑，完全参考现有startProgress模式，确保在合适的时机开始轮询查询
    *   原因：处理用户子提示，修正对mobile/video/sendTts接口的理解和轮询时机
    *   阻碍：无
    *   状态：重构完成，等待用户确认

*   2025-01-14 (入门版试听进度优化)
    *   步骤：1. 优化入门版试听的进度显示和轮询逻辑 (用户子提示迭代)
    *   修改：
        - 为入门版试听模式也添加loading弹窗和进度显示
        - 修改handleBeginnerTtsAudio：试听和视频合成都显示loading
        - 添加updateBeginnerLoadingText方法：动态更新loading文字显示进度百分比
        - 优化getBeginnerSoundRefresh：音频未完成时继续轮询，而不是停止
        - 确保试听模式也能正确等待音频生成完成后播放
        - 统一了视频合成和试听的loading隐藏逻辑
    *   更改摘要：完善了入门版试听的用户体验，添加了进度显示，确保试听也能正确等待音频生成
    *   原因：处理用户子提示，避免试听时提示生成失败，改为等待轮询结果
    *   阻碍：无
    *   状态：优化完成，等待用户确认

*   2025-01-14 (修正入门版接口逻辑)
    *   步骤：1. 修正getVideoSendTts方法中对入门版接口返回值的错误判断 (用户子提示迭代)
    *   修改：
        - 移除了getVideoSendTts方法中对result.data.url的判断逻辑
        - 入门版使用mobile/video/sendTts接口只返回ID，不返回音频链接
        - 简化了非入门版（专业版、高保真）的处理逻辑
        - 确保入门版完全通过handleBeginnerTtsAudio方法处理
        - 避免了入门版试听时因为没有URL字段而提示"音频生成失败"
    *   更改摘要：修正了对mobile/video/sendTts接口返回值的理解，确保入门版不会因为缺少URL字段而报错
    *   原因：处理用户子提示，修正对接口返回值的错误判断
    *   阻碍：无
    *   状态：修正完成，等待用户确认

*   2025-01-14 (同步voiceType值)
    *   步骤：1. 在菜单切换时同步更新voiceType的值 (用户子提示迭代)
    *   修改：
        - 修改setPrimaryMenu方法：切换到"我的音色"时设置voiceType=1，切换到"公共音色"时设置voiceType=4
        - 修改setSecondaryMenu方法：入门版voiceType=1，专业版voiceType=3，高保真voiceType=4
        - 修改selVoiceTabs方法：选择音色时根据菜单类型设置正确的voiceType值
        - 修改getBatch方法：初始化弹窗时设置默认voiceType=1（入门版）
        - 确保公共音色的voiceType设置为4（使用高保真接口）
    *   更改摘要：实现了菜单切换与voiceType值的同步，确保音频合成接口的正确调用
    *   原因：处理用户子提示，确保voiceType值与当前选择的音色类型一致
    *   阻碍：无
    *   状态：同步完成，等待用户确认

*   2025-01-14 (修正videoSendTts接口返回值处理)
    *   步骤：1. 修正对videoSendTts接口返回数据格式的处理 (用户子提示迭代)
    *   修改：
        - 修正handleBeginnerTtsAudio方法中对result.data的处理
        - videoSendTts接口返回的data直接是ID字符串，不是对象
        - 将result.data.id改为直接使用result.data作为任务ID
        - 返回示例：{data: "792", errno: 0, message: "提交成功"}
    *   更改摘要：修正了对videoSendTts接口返回值格式的理解，确保正确获取任务ID
    *   原因：处理用户子提示，修正对接口返回数据结构的错误理解
    *   阻碍：无
    *   状态：修正完成，等待用户确认

*   2025-01-14 (修复soundRefresh接口调用问题)
    *   步骤：1. 排查并修复soundRefresh接口未被调用的问题 (用户子提示迭代)
    *   修改：
        - 在handleBeginnerTtsAudio方法中添加this.isWhether = true，确保后续soundRefresh调用不被阻止
        - 添加调试日志：在startBeginnerProgress和getBeginnerSoundRefresh方法中添加console.log
        - 确认soundRefresh接口在api.js中正确定义为'mobile/video/soundRefresh'
        - 修复了isWhether状态管理导致的接口调用阻塞问题
    *   更改摘要：解决了soundRefresh接口未被调用的问题，添加调试日志便于排查
    *   原因：处理用户子提示，修复接口调用逻辑错误
    *   阻碍：无
    *   状态：修复完成，等待用户确认

*   2025-01-14 (添加资源清理机制)
    *   步骤：1. 添加页面卸载和弹窗关闭时的资源清理机制 (用户子提示迭代)
    *   修改：
        - 在onUnload中添加cleanupAllResources方法调用，清理所有资源
        - 在closeBatch中添加cleanupVoiceSelectionResources方法调用，清理音色选择相关资源
        - 新增cleanupVoiceSelectionResources方法：停止音频播放、停止入门版进度定时器、重置状态
        - 新增cleanupAllResources方法：页面卸载时的完整资源清理
        - 新增clearAllVoicePlayingStates方法：清理所有音色列表的播放状态
        - 确保弹窗关闭时停止所有音频播放和定时器循环
    *   更改摘要：完善了资源管理，确保页面卸载和弹窗关闭时正确清理所有相关资源
    *   原因：处理用户子提示，避免内存泄漏和资源占用
    *   阻碍：无
    *   状态：添加完成，等待用户确认

*   2025-01-14 (修正入门版音频结果处理逻辑)
    *   步骤：1. 参考getSoundRefresh函数，修正getBeginnerSoundRefresh的结果处理方式 (用户子提示迭代)
    *   修改：
        - 分析了原有getSoundRefresh函数的处理逻辑：检测到result.data存在时调用getVideoSoundList获取音频列表
        - 修改getBeginnerSoundRefresh：音频合成完成后调用getBeginnerVideoSoundList获取音频列表
        - 新增getBeginnerVideoSoundList方法：调用videoSoundList接口获取最新音频，支持试听和视频合成两种模式
        - 试听模式：获取音频列表后直接播放最新音频
        - 视频合成模式：获取音频列表后使用最新音频进行视频合成
        - 确保与原有音频处理逻辑保持一致
    *   更改摘要：修正了入门版音频合成完成后的结果处理方式，确保与现有逻辑一致
    *   原因：处理用户子提示，参考现有getSoundRefresh函数的正确处理方式
    *   阻碍：无
    *   状态：修正完成，等待用户确认

*   2025-01-14 (修复入门版生成视频逻辑)
    *   步骤：1. 修复入门版生成视频时的音频检查逻辑 (用户子提示迭代)
    *   修改：
        - 修改getBatch方法中的入门版音频检查逻辑
        - 原逻辑：入门版没有音频时直接提示"音频合成中,请耐心等待!"并返回
        - 新逻辑：入门版没有音频时先检查文案，然后自动调用handleBeginnerTtsAudio生成音频
        - 添加文案检查：确保有文案且长度不超过1000字
        - 自动调用音频合成：handleBeginnerTtsAudio(1, '')，type=1表示视频合成模式
        - 确保入门版生成视频的完整流程：文案检查 → 音频生成 → 视频合成
    *   更改摘要：修复了入门版生成视频时被阻断的问题，实现了自动音频生成的完整流程
    *   原因：处理用户子提示，修复入门版生成视频的逻辑错误
    *   阻碍：无
    *   状态：修复完成，等待用户确认

*   2025-01-14 (修正megaTtsSynthesize接口type参数)
    *   步骤：1. 修正megaTtsSynthesize接口的type参数传值逻辑 (用户子提示迭代)
    *   修改：
        - 修正handleMegaTtsAudio方法中megaTtsSynthesize接口的type参数
        - 原逻辑：基于voiceType判断 (this.voicesObj.voiceType == 4 ? 1 : 2)
        - 新逻辑：基于音色来源判断 (this.primaryMenu === 'public' ? 2 : 1)
        - 我的-高保真：type = 1
        - 公共音色：type = 2
        - 确保接口参数与后端期望的值匹配
    *   更改摘要：修正了megaTtsSynthesize接口的type参数，确保我的高保真和公共音色使用正确的参数值
    *   原因：处理用户子提示，修正接口参数的传值逻辑
    *   阻碍：无
    *   状态：修正完成，等待用户确认

*   2025-01-14 (实现音色弹窗菜单状态记忆)
    *   步骤：1. 修改音色弹窗打开逻辑，保持用户上一次选择的菜单状态 (用户子提示迭代)
    *   修改：
        - 修改getBatch方法中的音色弹窗初始化逻辑
        - 移除了强制重置菜单状态的逻辑，保持用户上一次的选择
        - 弹窗打开时根据当前菜单状态（primaryMenu, secondaryMenu）设置对应的voiceType
        - 根据当前菜单状态加载对应的音色数据（我的音色或公共音色）
        - 添加调试日志显示当前菜单状态
        - 确保用户体验的连续性，记住用户的偏好设置
    *   更改摘要：实现了音色弹窗的菜单状态记忆功能，用户再次打开弹窗时会显示上一次选择的菜单
    *   原因：处理用户子提示，提升用户体验，避免每次都重置到默认状态
    *   阻碍：无
    *   状态：实现完成，等待用户确认

*   2025-01-14 (重构二级菜单样式)
    *   步骤：1. 将音色选择的二级菜单样式改为参考digital-assets页面的一级菜单样式 (用户子提示迭代)
    *   修改：
        - 修改HTML结构：将原有的简单flex布局改为带底部线条的标签栏样式
        - 添加scroll-view支持横向滚动
        - 使用secondary-tabbar-item、secondary-tabbar-text、secondary-tabbar-line等新的class名
        - 重写CSS样式：参考digital-assets.vue页面的tabbar样式结构
        - 使用当前页面的绿色渐变：linear-gradient(90deg, #4FFF76 0%, #00ECFF 100%)
        - 添加底部线条动画效果和阴影效果
        - 保持响应式设计和平滑过渡动画
    *   更改摘要：重构了二级菜单的视觉样式，采用了更现代的带底部线条的标签栏设计
    *   原因：处理用户子提示，统一页面设计风格，提升视觉体验
    *   阻碍：无
    *   状态：重构完成，等待用户确认

*   2025-01-14 (统一公共音色二级菜单样式)
    *   步骤：1. 将公共音色的二级菜单也改为相同的标签栏样式 (用户子提示迭代)
    *   修改：
        - 修改公共音色二级菜单的HTML结构，使用与我的音色相同的样式类
        - 将public-tab-item等旧样式类改为secondary-tabbar-item等新样式类
        - 删除了旧的public-tabs-scroll、public-tab-item、public-tab-normal、public-tab-active样式
        - 现在我的音色和公共音色的二级菜单都使用统一的样式系统
        - 保持了相同的绿色渐变底部线条和动画效果
        - 确保了两种菜单的视觉一致性
    *   更改摘要：统一了我的音色和公共音色的二级菜单样式，实现了完全一致的视觉体验
    *   原因：处理用户子提示，确保所有二级菜单的样式统一
    *   阻碍：无
    *   状态：统一完成，等待用户确认

*   2025-01-14 (修复菜单切换时的音色匹配问题)
    *   步骤：1. 解决菜单切换时音色模式与选中音色不匹配导致的接口调用错误 (用户子提示迭代)
    *   修改：
        - 在setPrimaryMenu方法中添加clearSelectedVoice()调用，切换一级菜单时清空音色选择
        - 在setSecondaryMenu方法中添加clearSelectedVoice()调用，切换二级菜单时清空音色选择
        - 在setPublicScenario方法中添加clearSelectedVoice()调用，切换公共音色分类时清空音色选择
        - 新增clearSelectedVoice方法：重置voicesObj为默认空状态
        - 确保菜单切换后用户必须重新选择音色，避免接口调用错误
        - 防止了"选中公共音色但菜单切换到专业版"导致的接口不匹配问题
    *   更改摘要：修复了菜单切换时音色模式与选中音色不匹配的问题，确保接口调用的正确性
    *   原因：处理用户子提示，避免菜单与音色不匹配导致的接口调用错误
    *   阻碍：无
    *   状态：修复完成，等待用户确认
