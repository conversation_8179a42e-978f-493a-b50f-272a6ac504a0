<template>
	<view v-if="uid">
		<view class="bg" :style="{'background-image': 'url('+imgUrl+'405.png'+')'}">

			<view
				:style="{'height':''+heightSystemss+'px','top':''+statusBarHeightss+'px','lineHeight':''+heightSystemss+'px','left':''+10+'px'}"
				class="iconDizhssi">
				<image @click="navig()" class="img-34" :src="imgUrl + '34.png'"></image>
				<view class="font-size_30rpx" @click="navig()">
					生成记录
				</view>
			</view>


			<mescroll-body ref="mescrollRef" :height="windowHeight+'rpx'" @init="mescrollInit" @down="downCallback"
				@up="upCallback" :up="upOption" :down="downOption">

				<block v-for="(item,index) in list" :key="index">
					<view class="list-public">
						<view class="list-top" :class="item.type == 16 ? 'color_20FF86' : 'color_E0FF1E'">
							{{item.type == 16 ? '网络提取' : '本地提取'}}
						</view>
						<view class="list-text"><text user-select>{{item.answer}}</text></view>
						<view class="display-a-js" style="padding: 0 20rpx 30rpx;">
							<view class="list-date">{{item.create_time}}</view>
							<view class="color_20FF86 font-weight_bold margin-left-auto" @click="copy(item.answer)">复制
							</view>
							<view @click="getClip(item.answer)" class="color_20FF86 font-weight_bold margin-left-auto">
								去创作</view>
							<image @click="getClip(item.answer)" class="img-417" :src="imgUrl+'417.png'"></image>
						</view>
					</view>
				</block>

			</mescroll-body>

		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {

				imgUrl: this.$imgUrl,

				uid: '',

				heightSystemss: '',
				statusBarHeightss: '',
				windowHeight: '',

				// 下拉配置项
				downOption: {
					auto: false
				},
				// 上拉配置项
				upOption: {
					auto: false
				},

				list: [],

			}
		},

		onLoad() {
			this.getSystemInfo();
		},

		onShow() {
			if (uni.getStorageSync('uid')) {
				this.uid = uni.getStorageSync('uid');
				this.$nextTick(() => {
					this.mescroll.resetUpScroll();
				});
			} else {
				uni.showModal({
					content: "请先登录",
					cancelText: "返回",
					confirmText: "去登录",
					success: (res) => {
						if (res.confirm) {
							uni.redirectTo({
								url: '/pages/auth/auth?type=1'
							})
						} else if (res.cancel) {
							this.navig();
						}
					}
				})
			}
		},

		methods: {

			copy(text) {
				uni.setClipboardData({
					data: text,
					success: () => {
						// 复制成功的回调
						this.$sun.toast('复制成功');
					},
					fail: (err) => {
						console.log("复制失败原因===>", err);
						// 复制失败的回调
						uni.showToast({
							title: '复制失败：' + err,
							icon: 'none'
						});
					}
					});
			},

			getClip(msgText) {

				uni.setStorageSync("answer", msgText);

				// uni.navigateTo({
				// 	url: '/pages/index/clip/clip?answerType=1'
				// })
				uni.navigateTo({
					url: '/pages/index/ipGather/detail'
				})
			},

			async upCallback(scroll) {
				const result = await this.$http.post({
					url: this.$api.aiCreateLog,
					data: {
						uid: uni.getStorageSync('uid'),
						type: '20',
						page: scroll.num,
						psize: 10
					}
				});
				if (result.errno == 0) {
					this.mescroll.endByPage(result.data.list.length, result.data.totalPage);
					if (scroll.num == 1) this.list = [];
					this.list = this.list.concat(result.data.list);
				}
			},

			navig() {
				let pages = getCurrentPages(); //获取所有页面栈实例列表

				if (pages.length == 1) {
					uni.reLaunch({
						url: '/pages/index/index'
					})
				} else {
					uni.navigateBack();
				}
			},
			getSystemInfo() {
				let menuButtonObject = uni.getMenuButtonBoundingClientRect(); //获取菜单按钮（右上角胶囊按钮）的布局位置信息。坐标信息以屏幕左上角为原点。
				uni.getSystemInfo({ //获取系统信息
					success: res => {
						let navHeight = menuButtonObject.height + (menuButtonObject.top - res
							.statusBarHeight) * 2; //导航栏高度=菜单按钮高度+（菜单按钮与顶部距离-状态栏高度）*2
						this.heightSystemss = navHeight;
						this.statusBarHeightss = res.statusBarHeight;
						this.windowHeight = res.windowHeight * 2 - 204;
					},
					fail(err) {
						// console.log(err);
					}
				})
			},
		}
	}
</script>

<style lang="scss">
	.list-date {
		color: #e7e7e7;
		font-size: 26rpx;
	}

	.img-417 {
		width: 32rpx;
		height: 32rpx;
	}

	.list-text {
		width: 710rpx;
		padding: 30rpx;
		color: #FFF;
		border-bottom: 1px solid rgba(246, 246, 246, 0.14);
		margin-bottom: 30rpx;
	}

	.list-top {
		width: 710rpx;
		background-color: #1B1B1B;
		border-radius: 10rpx 10rpx 0 0;
		padding: 30rpx 20rpx;
	}

	.list-public {
		background-color: #101010;
		padding: 0;
	}

	.bg {
		width: 750rpx;
		height: 580rpx;
		background-repeat: no-repeat;
		background-size: cover;
		padding: 200rpx 0 0;
	}

	.img-34 {
		width: 40rpx;
		height: 40rpx;
	}

	.iconDizhssi {
		position: absolute;
		z-index: 999;
		color: #FFFFFF;
		display: flex;
		align-items: center;
	}

	page {
		border: none;
		background: #000;
		width: 100%;
		overflow-x: hidden !important;
	}
</style>